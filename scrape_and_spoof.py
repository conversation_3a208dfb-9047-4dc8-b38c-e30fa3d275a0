#!/usr/bin/env python3
"""
Combined script that scrapes Instagram reels and then applies spoofing to them
"""

import os
from instagram_reels_scraper import InstagramReelsScraper
from reels_spoofer import batch_spoof_videos


def scrape_and_spoof():
    """
    Scrape Instagram reels and then apply spoofing to all downloaded videos
    """
    print("=== Instagram Reels Scraper & Spoofer ===")
    
    # Step 1: Scrape reels
    print("\n1. Scraping Instagram reels...")
    scraper = InstagramReelsScraper()
    scraper.scrape_reels()
    
    # Step 2: Check if any videos were downloaded
    download_folder = scraper.download_folder
    if not os.path.exists(download_folder):
        print(f"❌ Download folder {download_folder} not found")
        return
    
    video_files = [f for f in os.listdir(download_folder) if f.endswith('.mp4')]
    if not video_files:
        print(f"❌ No video files found in {download_folder}")
        return
    
    print(f"✅ Found {len(video_files)} downloaded videos")
    
    # Step 3: Apply spoofing to all downloaded videos
    print("\n2. Applying spoofing to downloaded videos...")
    output_folder = os.path.join("output_vids", "spoofed_reels")
    
    try:
        processed_files = batch_spoof_videos(download_folder, output_folder)
        
        if processed_files:
            print(f"✅ Successfully spoofed {len(processed_files)} videos!")
            print(f"📁 Spoofed videos saved to: {output_folder}")
            
            # Show summary
            print("\nProcessed files:")
            for i, output_file in enumerate(processed_files, 1):
                filename = os.path.basename(output_file)
                size = os.path.getsize(output_file)
                print(f"  {i}. {filename} ({size:,} bytes)")
        else:
            print("❌ No videos were successfully spoofed")
            
    except Exception as e:
        print(f"❌ Error during spoofing: {e}")


def main():
    """Main function"""
    # Check if ffmpeg is available
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ ffmpeg not found. Please install ffmpeg first.")
            print("   On macOS: brew install ffmpeg")
            print("   On Ubuntu: sudo apt install ffmpeg")
            print("   On Windows: Download from https://ffmpeg.org/download.html")
            return
        print("✅ ffmpeg is available")
    except FileNotFoundError:
        print("❌ ffmpeg not found. Please install ffmpeg first.")
        print("   On macOS: brew install ffmpeg")
        print("   On Ubuntu: sudo apt install ffmpeg")
        print("   On Windows: Download from https://ffmpeg.org/download.html")
        return
    
    scrape_and_spoof()


if __name__ == "__main__":
    main()
