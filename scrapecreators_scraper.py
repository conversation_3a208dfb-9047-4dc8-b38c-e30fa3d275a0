#!/usr/bin/env python3
"""
Instagram Reels Scraper using ScrapeCreators API
"""

import os
import requests
import urllib.request
import json


class ScrapeCreatorsReelsScraper:
    def __init__(self, api_key="mxZmTdPaM2P5vb4GS59iO1AgXYX2", download_folder="downloaded_reels"):
        """
        Initialize the ScrapeCreators scraper
        
        Args:
            api_key (str): ScrapeCreators API key
            download_folder (str): Folder to save downloaded videos
        """
        self.api_key = api_key
        self.download_folder = download_folder
        self.base_url = "https://api.scrapecreators.com/v1/instagram/user/reels/simple"
        
        # Create download folder if it doesn't exist
        os.makedirs(self.download_folder, exist_ok=True)
    
    def get_reels(self, handle, amount=12):
        """
        Fetch reels data from ScrapeCreators API
        
        Args:
            handle (str): Instagram username (without @)
            amount (int): Number of reels to fetch
        
        Returns:
            list: List of reel data or empty list if failed
        """
        try:
            print(f"Fetching {amount} reels from @{handle}...")
            
            params = {
                'handle': handle,
                'amount': amount
            }
            
            headers = {
                'x-api-key': self.api_key
            }
            
            response = requests.get(self.base_url, params=params, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Successfully fetched {len(data)} reels")
                return data
            else:
                print(f"❌ API request failed with status {response.status_code}")
                print(f"Response: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error fetching reels: {e}")
            return []
    
    def extract_video_url(self, media_data):
        """
        Extract the best quality video URL with audio from media data
        
        Args:
            media_data (dict): Media data from the API
        
        Returns:
            tuple: (video_url, audio_url) or (combined_url, None) if found, (None, None) if not found
        """
        # First, try to find a direct MP4 URL that might contain both video and audio
        direct_url_fields = ['video_url', 'video_download_url']
        for field in direct_url_fields:
            if field in media_data and media_data[field]:
                return (media_data[field], None)
        
        # If we have DASH manifest, extract video and audio URLs from it
        if 'video_dash_manifest' in media_data and media_data['video_dash_manifest']:
            video_url, audio_url = self.parse_dash_manifest(media_data['video_dash_manifest'])
            if video_url and audio_url:
                return (video_url, audio_url)
            elif video_url:  # Video only, no audio found
                return (video_url, None)
        
        # Fallback to video_versions (but these are usually video-only)
        if 'video_versions' in media_data and media_data['video_versions']:
            # Get the first (usually highest quality) video version
            return (media_data['video_versions'][0]['url'], None)
        
        return (None, None)
    
    def parse_dash_manifest(self, dash_manifest):
        """
        Parse DASH manifest to extract video and audio URLs
        
        Args:
            dash_manifest (str): DASH XML manifest
        
        Returns:
            tuple: (video_url, audio_url) or (None, None) if parsing fails
        """
        try:
            import xml.etree.ElementTree as ET
            
            root = ET.fromstring(dash_manifest)
            
            video_url = None
            audio_url = None
            
            # Find video and audio adaptation sets
            for adaptation_set in root.findall('.//{urn:mpeg:dash:schema:mpd:2011}AdaptationSet'):
                content_type = adaptation_set.get('contentType')
                
                if content_type == 'video':
                    # Get the highest quality video representation
                    representations = adaptation_set.findall('.//{urn:mpeg:dash:schema:mpd:2011}Representation')
                    if representations:
                        # Sort by bandwidth (higher is better quality)
                        representations.sort(key=lambda x: int(x.get('bandwidth', 0)), reverse=True)
                        best_video = representations[0]
                        base_url = best_video.find('.//{urn:mpeg:dash:schema:mpd:2011}BaseURL')
                        if base_url is not None:
                            video_url = base_url.text
                
                elif content_type == 'audio':
                    # Get the audio representation
                    representations = adaptation_set.findall('.//{urn:mpeg:dash:schema:mpd:2011}Representation')
                    if representations:
                        # Usually there's only one audio track
                        audio_repr = representations[0]
                        base_url = audio_repr.find('.//{urn:mpeg:dash:schema:mpd:2011}BaseURL')
                        if base_url is not None:
                            audio_url = base_url.text
            
            return (video_url, audio_url)
            
        except Exception as e:
            print(f"  ⚠️  Error parsing DASH manifest: {e}")
            return (None, None)
    
    def generate_filename(self, media_data, index):
        """
        Generate filename for the video
        
        Args:
            media_data (dict): Media data from API
            index (int): Index of the reel
        
        Returns:
            str: Full path to the output file
        """
        # Try to get the code from media data
        code = "unknown"
        if 'code' in media_data:
            code = media_data['code']
        elif 'shortcode' in media_data:
            code = media_data['shortcode']
        elif 'id' in media_data:
            code = str(media_data['id'])
        
        # Get handle from the first reel if available
        handle = "user"
        if 'owner' in media_data and 'username' in media_data['owner']:
            handle = media_data['owner']['username']
        
        filename = f"{handle}_{code}_{index}.mp4"
        return os.path.join(self.download_folder, filename)

    def download_video(self, video_url, filename, audio_url=None):
        """
        Download video from URL, optionally combining with separate audio

        Args:
            video_url (str): URL of the video to download
            filename (str): Local filename to save the video
            audio_url (str, optional): URL of separate audio track

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print(f"  📥 Downloading: {os.path.basename(filename)}")

            if audio_url:
                # Download video and audio separately, then combine with ffmpeg
                return self.download_and_combine(video_url, audio_url, filename)
            else:
                # Download single file (hopefully with audio included)
                return self.download_single_file(video_url, filename)

        except Exception as e:
            print(f"  ❌ Error downloading {os.path.basename(filename)}: {e}")
            return False

    def download_single_file(self, video_url, filename):
        """Download a single video file"""
        try:
            # Add headers to mimic a browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # Create request with headers
            req = urllib.request.Request(video_url, headers=headers)

            with urllib.request.urlopen(req) as response:
                with open(filename, 'wb') as f:
                    f.write(response.read())

            # Check file size
            file_size = os.path.getsize(filename)
            print(f"  ✅ Successfully downloaded: {os.path.basename(filename)} ({file_size:,} bytes)")
            return True

        except Exception as e:
            print(f"  ❌ Error downloading single file: {e}")
            return False

    def download_and_combine(self, video_url, audio_url, output_filename):
        """Download video and audio separately, then combine with ffmpeg"""
        try:
            import subprocess
            import tempfile

            print(f"  🎬 Downloading video and audio separately...")

            # Create temporary files for video and audio
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_video:
                temp_video_path = temp_video.name

            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_audio:
                temp_audio_path = temp_audio.name

            try:
                # Download video
                if not self.download_single_file(video_url, temp_video_path):
                    return False

                # Download audio
                if not self.download_single_file(audio_url, temp_audio_path):
                    return False

                print(f"  🔧 Combining video and audio with ffmpeg...")

                # Combine video and audio using ffmpeg
                ffmpeg_cmd = [
                    'ffmpeg',
                    '-i', temp_video_path,
                    '-i', temp_audio_path,
                    '-c:v', 'copy',  # Copy video stream without re-encoding
                    '-c:a', 'copy',  # Copy audio stream without re-encoding
                    '-shortest',     # End when shortest stream ends
                    '-y',           # Overwrite output file
                    output_filename
                ]

                result = subprocess.run(
                    ffmpeg_cmd,
                    capture_output=True,
                    text=True,
                    check=True
                )

                # Check final file size
                file_size = os.path.getsize(output_filename)
                print(f"  ✅ Successfully combined: {os.path.basename(output_filename)} ({file_size:,} bytes)")
                return True

            finally:
                # Clean up temporary files
                try:
                    os.unlink(temp_video_path)
                    os.unlink(temp_audio_path)
                except:
                    pass

        except subprocess.CalledProcessError as e:
            print(f"  ❌ Error combining video and audio: {e}")
            print(f"  stderr: {e.stderr}")
            return False
        except Exception as e:
            print(f"  ❌ Error in download_and_combine: {e}")
            return False

    def scrape_reels(self, handle, amount=12):
        """
        Main method to scrape reels from Instagram

        Args:
            handle (str): Instagram username (without @)
            amount (int): Number of reels to download

        Returns:
            list: List of downloaded file paths
        """
        print(f"🎬 Starting to scrape reels from @{handle}")

        # Get reels data from API
        reels_data = self.get_reels(handle, amount)
        if not reels_data:
            return []

        downloaded_files = []

        for i, reel in enumerate(reels_data, 1):
            print(f"\n[{i}/{len(reels_data)}] Processing reel...")

            # Extract media data
            media_data = reel.get('media', {})
            if not media_data:
                print(f"  ⚠️  No media data found for reel {i}")
                continue

            # Extract video and audio URLs
            video_url, audio_url = self.extract_video_url(media_data)
            if not video_url:
                print(f"  ⚠️  No video URL found for reel {i}")
                continue

            # Generate filename
            filename = self.generate_filename(media_data, i)

            # Download the video (with audio if available)
            if self.download_video(video_url, filename, audio_url):
                downloaded_files.append(filename)

        print(f"\n🎉 Scraping completed! Downloaded {len(downloaded_files)} reels to '{self.download_folder}'")

        if downloaded_files:
            print(f"\n📁 Downloaded files:")
            for i, file_path in enumerate(downloaded_files, 1):
                filename = os.path.basename(file_path)
                size = os.path.getsize(file_path)
                print(f"  {i}. {filename} ({size:,} bytes)")

        return downloaded_files


def main():
    """Command line interface"""
    import sys

    if len(sys.argv) < 2:
        print("Usage: python3 scrapecreators_scraper.py <handle> [amount] [api_key]")
        print("Example: python3 scrapecreators_scraper.py fox1naomi 12")
        return

    handle = sys.argv[1]
    amount = int(sys.argv[2]) if len(sys.argv) > 2 else 12
    api_key = sys.argv[3] if len(sys.argv) > 3 else "mxZmTdPaM2P5vb4GS59iO1AgXYX2"

    scraper = ScrapeCreatorsReelsScraper(api_key=api_key)
    scraper.scrape_reels(handle, amount)


if __name__ == "__main__":
    main()
