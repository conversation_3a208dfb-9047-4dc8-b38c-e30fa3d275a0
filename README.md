# Instagram Reels Scraper & Spoofer

A Python toolkit that downloads Instagram reels and applies subtle modifications to avoid detection while keeping them visually identical.

## Features

### Scraper Features
- Downloads all reels from a specified Instagram account
- Configurable download settings
- Customizable filename patterns
- Respects API rate limits
- Skips non-video content automatically
- Creates organized download folders

### Spoofer Features
- Applies subtle resolution changes (0.1% - 1% reduction)
- Modifies video bitrate with high-quality encoding
- Changes metadata creation time with randomization
- Batch processing support
- Preserves visual quality while altering file fingerprint

## Files

### Core Scripts
- `instagram_reels_scraper.py` - Main scraper script
- `reels_spoofer.py` - Video spoofing functionality
- `scrape_and_spoof.py` - Combined scraper and spoofer
- `config.py` - Configuration file

### Test Scripts
- `test_scraper.py` - Test script with mock data
- `test_api.py` - API testing script
- `test_reels_spoofer.py` - Test script for video spoofing

## Setup

1. **Install Python 3** (if not already installed)

2. **Install ffmpeg** (required for video spoofing):
   - **macOS**: `brew install ffmpeg`
   - **Ubuntu/Debian**: `sudo apt install ffmpeg`
   - **Windows**: Download from [https://ffmpeg.org/download.html](https://ffmpeg.org/download.html)

3. **Configure the scraper** by editing `config.py`:
   ```python
   # Instagram account to scrape (without @ symbol)
   INSTAGRAM_ACCOUNT = "your_target_account"
   
   # API Configuration
   RAPIDAPI_KEY = "your_rapidapi_key_here"
   
   # Download settings
   DOWNLOAD_FOLDER = "downloaded_reels"
   MAX_REELS = 50  # Set to None for unlimited
   ```

4. **Get a RapidAPI key**:
   - Go to [RapidAPI Instagram Scraper](https://rapidapi.com/hub)
   - Search for "Instagram Scraper Stable API"
   - Subscribe to the API
   - Copy your API key to `config.py`

## Usage

### Scraper Only
```bash
# Run the main scraper
python3 instagram_reels_scraper.py

# Test with mock data
python3 test_scraper.py

# Test API connection
python3 test_api.py
```

### Video Spoofing
```bash
# Spoof a single video
python3 reels_spoofer.py input_video.mp4 output_video.mp4

# Batch spoof all videos in a directory
python3 reels_spoofer.py --batch input_directory output_directory

# Test the spoofing functionality
python3 test_reels_spoofer.py
```

### Combined Scraping and Spoofing
```bash
# Scrape reels and automatically apply spoofing
python3 scrape_and_spoof.py
```

## Configuration Options

### `config.py` settings:

- **INSTAGRAM_ACCOUNT**: Target Instagram username (without @)
- **RAPIDAPI_KEY**: Your RapidAPI key
- **DOWNLOAD_FOLDER**: Folder to save downloaded reels
- **MAX_REELS**: Maximum number of reels to download (None = unlimited)
- **FILENAME_PATTERN**: Pattern for naming downloaded files

### Filename Pattern Variables:
- `{username}` - Instagram account name
- `{media_id}` - Unique media ID
- `{timestamp}` - Current timestamp
- `{index}` - Sequential number

Example: `"{username}_{media_id}_{index}.mp4"` → `"account_12345_1.mp4"`

## Video Spoofing Details

The `reels_spoofer` function applies subtle modifications to video files:

### Modifications Applied
1. **Resolution Scaling**: Random scale factor between 0.990-0.999 (0.1%-1% reduction)
2. **Quality Encoding**: Random CRF between 17-19 (high quality range)
3. **Metadata Changes**: Updates creation time to current time ± 30 minutes
4. **Encoding Preset**: Uses 'veryslow' preset for optimal compression

### Function Parameters
```python
reels_spoofer(
    input_video_path,           # Required: path to input video
    output_video_path=None,     # Optional: output path (auto-generated if None)
    scale_factor=None,          # Optional: custom scale factor (0.990-0.999)
    crf=None,                   # Optional: custom CRF value (17-19)
    preset=None                 # Optional: ffmpeg preset (default: 'veryslow')
)
```

### Example Usage
```python
from reels_spoofer import reels_spoofer, batch_spoof_videos

# Spoof a single video
result = reels_spoofer("input.mp4", "output.mp4")

# Batch process all videos in a directory
processed = batch_spoof_videos("input_dir", "output_dir")
```

## API Response Structure

The scraper expects the Instagram API to return:

### User Media Response:
```json
{
  "data": [
    {
      "code": "media_shortcode",
      "media_type": "video"
    }
  ]
}
```

### Media Data Response:
```json
{
  "id": "media_id",
  "media_type": "video",
  "is_video": true,
  "product_type": "clips",
  "video_url": "https://video-url.mp4"
}
```

## Error Handling

The scraper handles:
- API connection errors
- Invalid media codes
- Missing video URLs
- Download failures
- Rate limiting (1 second delay between requests)

## Limitations

- Requires active RapidAPI subscription
- Subject to Instagram's rate limits
- Only downloads public content
- Depends on third-party API availability

## Troubleshooting

1. **"You are not subscribed to this API"**
   - Check your RapidAPI subscription status
   - Verify your API key is correct

2. **No reels found**
   - Verify the account name is correct
   - Check if the account has public reels
   - Try with a different account

3. **Download failures**
   - Check internet connection
   - Verify download folder permissions
   - Check available disk space

## Legal Notice

This tool is for educational purposes only. Always respect:
- Instagram's Terms of Service
- Copyright laws
- Privacy rights
- Rate limits and API terms

Use responsibly and only download content you have permission to access.
