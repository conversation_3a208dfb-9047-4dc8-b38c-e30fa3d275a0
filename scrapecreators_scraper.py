#!/usr/bin/env python3
"""
Instagram Reels Scraper using ScrapeCreators API
Downloads all reels from a specified Instagram account using the ScrapeCreators API
"""

import requests
import os
import urllib.request
import time
from datetime import datetime
from pathlib import Path


class ScrapeCreatorsReelsScraper:
    def __init__(self, api_key="mxZmTdPaM2P5vb4GS59iO1AgXYX2", download_folder="downloaded_reels"):
        self.api_key = api_key
        self.base_url = "https://api.scrapecreators.com/v1/instagram/user/reels/simple"
        self.download_folder = download_folder
        
        # Create download folder if it doesn't exist
        if not os.path.exists(self.download_folder):
            os.makedirs(self.download_folder)
    
    def get_reels(self, handle, amount=12):
        """
        Get reels from a user's profile using ScrapeCreators API
        
        Args:
            handle (str): Instagram username (without @)
            amount (int): Number of reels to fetch (default: 12)
        
        Returns:
            list: List of reel data or None if failed
        """
        headers = {
            "x-api-key": self.api_key
        }
        
        params = {
            "handle": handle,
            "amount": amount
        }
        
        try:
            print(f"Fetching {amount} reels from @{handle}...")
            response = requests.get(self.base_url, headers=headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Successfully fetched {len(data)} reels")
                return data
            else:
                print(f"❌ Error fetching reels: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error connecting to API: {e}")
            return None
    
    def extract_video_url(self, media_data):
        """
        Extract the best quality video URL from media data
        
        Args:
            media_data (dict): Media data from the API
        
        Returns:
            str: Video URL or None if not found
        """
        # Try video_versions first (usually has multiple quality options)
        if 'video_versions' in media_data and media_data['video_versions']:
            # Get the first (usually highest quality) video version
            return media_data['video_versions'][0]['url']
        
        # Fallback to other possible video URL fields
        video_url_fields = ['video_url', 'video_download_url']
        for field in video_url_fields:
            if field in media_data and media_data[field]:
                return media_data[field]
        
        return None
    
    def generate_filename(self, media_data, index):
        """
        Generate filename for the downloaded video
        
        Args:
            media_data (dict): Media data from the API
            index (int): Sequential index for the file
        
        Returns:
            str: Full path for the output file
        """
        # Extract useful info for filename
        media_id = media_data.get('id', 'unknown')
        code = media_data.get('code', 'unknown')
        username = media_data.get('owner', {}).get('username', 'unknown')
        
        # Create filename with media code and index
        filename = f"{username}_{code}_{index}.mp4"
        
        return os.path.join(self.download_folder, filename)
    
    def download_video(self, video_url, filename):
        """
        Download video from URL
        
        Args:
            video_url (str): URL of the video to download
            filename (str): Local filename to save the video
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print(f"  📥 Downloading: {os.path.basename(filename)}")
            
            # Add headers to mimic a browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # Create request with headers
            req = urllib.request.Request(video_url, headers=headers)
            
            with urllib.request.urlopen(req) as response:
                with open(filename, 'wb') as f:
                    f.write(response.read())
            
            # Check file size
            file_size = os.path.getsize(filename)
            print(f"  ✅ Successfully downloaded: {os.path.basename(filename)} ({file_size:,} bytes)")
            return True
            
        except Exception as e:
            print(f"  ❌ Error downloading {os.path.basename(filename)}: {e}")
            return False
    
    def scrape_reels(self, handle, amount=12):
        """
        Main method to scrape reels from an Instagram account
        
        Args:
            handle (str): Instagram username (without @)
            amount (int): Number of reels to download (default: 12)
        
        Returns:
            list: List of successfully downloaded file paths
        """
        print(f"🎬 Starting to scrape reels from @{handle}")
        
        # Get reels data from API
        reels_data = self.get_reels(handle, amount)
        if not reels_data:
            print("❌ Failed to get reels data")
            return []
        
        downloaded_files = []
        
        for i, reel_item in enumerate(reels_data, 1):
            print(f"\n[{i}/{len(reels_data)}] Processing reel...")
            
            # Extract media data
            if 'media' not in reel_item:
                print(f"  ⚠️  No media data found in reel {i}")
                continue
            
            media_data = reel_item['media']
            
            # Extract video URL
            video_url = self.extract_video_url(media_data)
            if not video_url:
                print(f"  ⚠️  No video URL found for reel {i}")
                continue
            
            # Generate filename
            filename = self.generate_filename(media_data, i)
            
            # Download the video
            if self.download_video(video_url, filename):
                downloaded_files.append(filename)
            
            # Be respectful to the servers
            time.sleep(0.5)
        
        print(f"\n🎉 Scraping completed! Downloaded {len(downloaded_files)} reels to '{self.download_folder}'")
        return downloaded_files


def main():
    """Example usage of the scraper"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python3 scrapecreators_scraper.py <instagram_handle> [amount]")
        print("Example: python3 scrapecreators_scraper.py fox1naomi 12")
        return
    
    handle = sys.argv[1]
    amount = int(sys.argv[2]) if len(sys.argv) > 2 else 12
    
    scraper = ScrapeCreatorsReelsScraper()
    downloaded_files = scraper.scrape_reels(handle, amount)
    
    if downloaded_files:
        print(f"\n📁 Downloaded files:")
        for i, file_path in enumerate(downloaded_files, 1):
            file_size = os.path.getsize(file_path)
            print(f"  {i}. {os.path.basename(file_path)} ({file_size:,} bytes)")


if __name__ == "__main__":
    main()
