#!/usr/bin/env python3
"""
Test script for the reels_spoofer function
"""

import os
from reels_spoofer import reels_spoofer, batch_spoof_videos


def test_single_video():
    """Test spoofing a single video"""
    # Check if there are any downloaded reels to test with
    downloaded_dir = "downloaded_reels"
    
    if not os.path.exists(downloaded_dir):
        print(f"Directory {downloaded_dir} not found. Please run the scraper first.")
        return
    
    # Find the first video file
    video_files = [f for f in os.listdir(downloaded_dir) if f.endswith('.mp4')]
    
    if not video_files:
        print(f"No video files found in {downloaded_dir}")
        return
    
    # Test with the first video
    input_video = os.path.join(downloaded_dir, video_files[0])
    output_video = os.path.join("output_vids", f"sp_{video_files[0]}")

    # Create output directory if it doesn't exist
    os.makedirs("output_vids", exist_ok=True)

    print(f"Testing with video: {input_video}")
    result = reels_spoofer(input_video, output_video)

    if result:
        print(f"✅ Successfully created processed video: {result}")

        # Show file sizes for comparison
        original_size = os.path.getsize(input_video)
        processed_size = os.path.getsize(result)

        print(f"Original size: {original_size:,} bytes")
        print(f"Processed size: {processed_size:,} bytes")
        print(f"Size difference: {processed_size - original_size:+,} bytes")
    else:
        print("❌ Failed to create processed video")


def test_batch_processing():
    """Test batch processing of videos"""
    downloaded_dir = "downloaded_reels"
    
    if not os.path.exists(downloaded_dir):
        print(f"Directory {downloaded_dir} not found. Please run the scraper first.")
        return
    
    # Check if there are video files
    video_files = [f for f in os.listdir(downloaded_dir) if f.endswith('.mp4')]
    
    if not video_files:
        print(f"No video files found in {downloaded_dir}")
        return
    
    print(f"Found {len(video_files)} video files for batch processing")
    
    # Process all videos
    output_dir = os.path.join("output_vids", "batch_sp")
    processed_files = batch_spoof_videos(downloaded_dir, output_dir)

    print(f"✅ Batch processing completed! Processed {len(processed_files)} videos.")
    
    # Show summary
    for i, output_file in enumerate(processed_files, 1):
        size = os.path.getsize(output_file)
        print(f"{i}. {os.path.basename(output_file)} - {size:,} bytes")


def main():
    """Run tests"""
    print("=== Reels Spoofer Test ===")
    
    # Check if ffmpeg is available
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ ffmpeg not found. Please install ffmpeg first.")
            return
        print("✅ ffmpeg is available")
    except FileNotFoundError:
        print("❌ ffmpeg not found. Please install ffmpeg first.")
        return
    
    print("\n1. Testing single video spoofing...")
    test_single_video()
    
    print("\n" + "="*50)
    print("2. Testing batch video spoofing...")
    test_batch_processing()


if __name__ == "__main__":
    main()
