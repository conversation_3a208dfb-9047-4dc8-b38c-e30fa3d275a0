#!/usr/bin/env python3
"""
<PERSON><PERSON> Spoofer
Applies subtle modifications to video files to avoid detection while keeping them visually identical.
Uses ffmpeg to modify resolution, bitrate, and metadata.
"""

import os
import subprocess
import random
from datetime import datetime, timedelta
from pathlib import Path


def reels_spoofer(input_video_path, output_video_path=None, scale_factor=None, crf=None, preset=None):
    """
    Apply subtle modifications to a video file using ffmpeg.
    
    Args:
        input_video_path (str): Path to the input video file
        output_video_path (str, optional): Path for the output video. If None, adds '_spoofed' suffix
        scale_factor (float, optional): Scale factor for resolution (default: random between 0.990-0.999)
        crf (int, optional): Constant Rate Factor for quality (default: random between 17-19)
        preset (str, optional): ffmpeg preset (default: 'veryslow')
    
    Returns:
        str: Path to the output video file if successful, None if failed
    
    Raises:
        FileNotFoundError: If input video doesn't exist
        subprocess.CalledProcessError: If ffmpeg command fails
    """
    
    # Validate input file
    if not os.path.exists(input_video_path):
        raise FileNotFoundError(f"Input video file not found: {input_video_path}")
    
    # Generate output path if not provided
    if output_video_path is None:
        input_path = Path(input_video_path)
        output_video_path = str(input_path.parent / f"{input_path.stem}_spoofed{input_path.suffix}")
    
    # Set default parameters with randomization
    if scale_factor is None:
        # Random scale factor between 0.990 and 0.999 (1% to 0.1% reduction)
        scale_factor = round(random.uniform(0.990, 0.999), 3)
    
    if crf is None:
        # Random CRF between 17-19 (high quality range)
        crf = random.randint(17, 19)
    
    if preset is None:
        preset = 'veryslow'
    
    # Generate random creation time (current time + random offset)
    current_time = datetime.now()
    # Add random offset between -30 minutes to +30 minutes
    random_offset = timedelta(minutes=random.randint(-30, 30))
    creation_time = current_time + random_offset
    creation_time_str = creation_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    
    # Build ffmpeg command
    ffmpeg_cmd = [
        'ffmpeg',
        '-i', input_video_path,
        '-vf', f'scale=trunc(iw*{scale_factor}/2)*2:trunc(ih*{scale_factor}/2)*2',
        '-c:v', 'libx264',
        '-crf', str(crf),
        '-preset', preset,
        '-metadata', f'creation_time={creation_time_str}',
        '-y',  # Overwrite output file if it exists
        output_video_path
    ]
    
    try:
        print(f"Processing video: {input_video_path}")
        print(f"Scale factor: {scale_factor}")
        print(f"CRF: {crf}")
        print(f"Preset: {preset}")
        print(f"New creation time: {creation_time_str}")
        print(f"Output: {output_video_path}")
        
        # Run ffmpeg command
        result = subprocess.run(
            ffmpeg_cmd,
            capture_output=True,
            text=True,
            check=True
        )
        
        print("Video processing completed successfully!")
        return output_video_path
        
    except subprocess.CalledProcessError as e:
        print(f"Error running ffmpeg: {e}")
        print(f"Command: {' '.join(ffmpeg_cmd)}")
        print(f"stderr: {e.stderr}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None


def batch_spoof_videos(input_directory, output_directory=None):
    """
    Apply spoofing to all video files in a directory.
    
    Args:
        input_directory (str): Directory containing input videos
        output_directory (str, optional): Directory for output videos. If None, uses input_directory/spoofed
    
    Returns:
        list: List of successfully processed output file paths
    """
    
    if not os.path.exists(input_directory):
        raise FileNotFoundError(f"Input directory not found: {input_directory}")
    
    # Set output directory
    if output_directory is None:
        output_directory = os.path.join(input_directory, 'spoofed')
    
    # Create output directory if it doesn't exist
    os.makedirs(output_directory, exist_ok=True)
    
    # Common video file extensions
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
    
    processed_files = []
    
    # Process all video files in the directory
    for filename in os.listdir(input_directory):
        file_path = os.path.join(input_directory, filename)
        
        # Skip if not a file or not a video
        if not os.path.isfile(file_path):
            continue
            
        file_ext = Path(filename).suffix.lower()
        if file_ext not in video_extensions:
            continue
        
        # Generate output path
        output_path = os.path.join(output_directory, filename)
        
        # Process the video
        result = reels_spoofer(file_path, output_path)
        if result:
            processed_files.append(result)
        
        print("-" * 50)  # Separator between files
    
    print(f"Batch processing completed! Processed {len(processed_files)} videos.")
    return processed_files


def main():
    """Example usage of the reels_spoofer function"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python reels_spoofer.py <input_video> [output_video]")
        print("   or: python reels_spoofer.py --batch <input_directory> [output_directory]")
        return
    
    if sys.argv[1] == '--batch':
        if len(sys.argv) < 3:
            print("Batch mode requires input directory")
            return
        
        input_dir = sys.argv[2]
        output_dir = sys.argv[3] if len(sys.argv) > 3 else None
        
        batch_spoof_videos(input_dir, output_dir)
    else:
        input_video = sys.argv[1]
        output_video = sys.argv[2] if len(sys.argv) > 2 else None
        
        result = reels_spoofer(input_video, output_video)
        if result:
            print(f"Successfully created spoofed video: {result}")
        else:
            print("Failed to process video")


if __name__ == "__main__":
    main()
