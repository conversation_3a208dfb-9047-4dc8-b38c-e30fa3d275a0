#!/usr/bin/env python3
"""
Combined script that scrapes Instagram reels using ScrapeCreators API and then applies spoofing
"""

import os
from scrapecreators_scraper import ScrapeCreatorsReelsScraper
from reels_spoofer import batch_spoof_videos


def scrape_and_spoof_v2(handle, amount=12, api_key="mxZmTdPaM2P5vb4GS59iO1AgXYX2"):
    """
    Scrape Instagram reels using ScrapeCreators API and then apply spoofing to all downloaded videos
    
    Args:
        handle (str): Instagram username (without @)
        amount (int): Number of reels to download (default: 12)
        api_key (str): ScrapeCreators API key
    
    Returns:
        dict: Summary of the operation with downloaded and spoofed file counts
    """
    print("=== Instagram Reels Scraper & Spoofer v2 ===")
    print(f"Using ScrapeCreators API to fetch reels from @{handle}")
    
    # Step 1: Scrape reels
    print(f"\n1. 🎬 Scraping {amount} reels from @{handle}...")
    scraper = ScrapeCreatorsReelsScraper(api_key=api_key)
    downloaded_files = scraper.scrape_reels(handle, amount)
    
    if not downloaded_files:
        print("❌ No videos were downloaded")
        return {"downloaded": 0, "spoofed": 0, "error": "No videos downloaded"}
    
    print(f"✅ Successfully downloaded {len(downloaded_files)} reels")
    
    # Step 2: Apply spoofing to all downloaded videos
    print(f"\n2. 🎭 Applying spoofing to {len(downloaded_files)} downloaded videos...")
    output_folder = os.path.join("output_vids", "spoofed_reels_v2")
    
    try:
        processed_files = batch_spoof_videos(scraper.download_folder, output_folder)
        
        if processed_files:
            print(f"✅ Successfully spoofed {len(processed_files)} videos!")
            print(f"📁 Spoofed videos saved to: {output_folder}")
            
            # Show summary
            print(f"\n📊 Summary:")
            print(f"  • Downloaded: {len(downloaded_files)} reels")
            print(f"  • Spoofed: {len(processed_files)} videos")
            print(f"  • Success rate: {len(processed_files)/len(downloaded_files)*100:.1f}%")
            
            print(f"\n📁 Original files (in {scraper.download_folder}):")
            for i, file_path in enumerate(downloaded_files, 1):
                filename = os.path.basename(file_path)
                size = os.path.getsize(file_path)
                print(f"  {i}. {filename} ({size:,} bytes)")
            
            print(f"\n🎭 Spoofed files (in {output_folder}):")
            for i, file_path in enumerate(processed_files, 1):
                filename = os.path.basename(file_path)
                size = os.path.getsize(file_path)
                print(f"  {i}. {filename} ({size:,} bytes)")
            
            return {
                "downloaded": len(downloaded_files),
                "spoofed": len(processed_files),
                "downloaded_files": downloaded_files,
                "spoofed_files": processed_files,
                "success_rate": len(processed_files)/len(downloaded_files)*100
            }
        else:
            print("❌ No videos were successfully spoofed")
            return {"downloaded": len(downloaded_files), "spoofed": 0, "error": "Spoofing failed"}
            
    except Exception as e:
        print(f"❌ Error during spoofing: {e}")
        return {"downloaded": len(downloaded_files), "spoofed": 0, "error": str(e)}


def main():
    """Main function with command line interface"""
    import sys
    
    # Check if ffmpeg is available
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ ffmpeg not found. Please install ffmpeg first.")
            print("   On macOS: brew install ffmpeg")
            print("   On Ubuntu: sudo apt install ffmpeg")
            print("   On Windows: Download from https://ffmpeg.org/download.html")
            return
        print("✅ ffmpeg is available")
    except FileNotFoundError:
        print("❌ ffmpeg not found. Please install ffmpeg first.")
        print("   On macOS: brew install ffmpeg")
        print("   On Ubuntu: sudo apt install ffmpeg")
        print("   On Windows: Download from https://ffmpeg.org/download.html")
        return
    
    # Parse command line arguments
    if len(sys.argv) < 2:
        print("Usage: python3 scrape_and_spoof_v2.py <instagram_handle> [amount] [api_key]")
        print("Example: python3 scrape_and_spoof_v2.py fox1naomi 12")
        return
    
    handle = sys.argv[1]
    amount = int(sys.argv[2]) if len(sys.argv) > 2 else 12
    api_key = sys.argv[3] if len(sys.argv) > 3 else "mxZmTdPaM2P5vb4GS59iO1AgXYX2"
    
    # Run the scrape and spoof process
    result = scrape_and_spoof_v2(handle, amount, api_key)
    
    # Print final summary
    print(f"\n🏁 Final Results:")
    print(f"   Downloaded: {result['downloaded']} reels")
    print(f"   Spoofed: {result['spoofed']} videos")
    
    if 'success_rate' in result:
        print(f"   Success Rate: {result['success_rate']:.1f}%")
    
    if 'error' in result:
        print(f"   Error: {result['error']}")


if __name__ == "__main__":
    main()
